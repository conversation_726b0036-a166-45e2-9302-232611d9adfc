package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// CreateUserWithReferral is the resolver for the createUserWithReferral field.
func (r *mutationResolver) CreateUserWithReferral(ctx context.Context, input gql_model.CreateUserWithReferralInput) (*gql_model.CreateUserResponse, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.CreateUserWithReferral(ctx, input)
}

// CreateUserInvitationCode is the resolver for the createUserInvitationCode field.
func (r *mutationResolver) CreateUserInvitationCode(ctx context.Context, input gql_model.CreateUserInvitationCodeInput) (*gql_model.CreateUserResponse, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.CreateUserInvitationCode(ctx, input)
}

// UpdateLevelCommission is the resolver for the updateLevelCommission field.
func (r *mutationResolver) UpdateLevelCommission(ctx context.Context, input gql_model.UpdateLevelCommissionInput) (*gql_model.UpdateLevelCommissionResponse, error) {
	levelResolver := resolvers.NewLevelResolver()
	return levelResolver.UpdateLevelCommission(ctx, input)
}

// ClaimAgentReferral is the resolver for the claimAgentReferral field.
func (r *mutationResolver) ClaimAgentReferral(ctx context.Context, input gql_model.ClaimAgentReferralInput) (*gql_model.ClaimResultResponse, error) {
	claimResolver := resolvers.NewClaimResolver()
	return claimResolver.ClaimAgentReferral(ctx, input)
}

// CreateInfiniteAgentConfig is the resolver for the createInfiniteAgentConfig field.
func (r *mutationResolver) CreateInfiniteAgentConfig(ctx context.Context, input gql_model.CreateInfiniteAgentConfigInput) (*gql_model.CreateInfiniteAgentConfigResponse, error) {
	infiniteAgentConfigResolver := resolvers.NewInfiniteAgentConfigResolver()
	return infiniteAgentConfigResolver.CreateInfiniteAgentConfig(ctx, input)
}

// UpdateInfiniteAgentConfig is the resolver for the updateInfiniteAgentConfig field.
func (r *mutationResolver) UpdateInfiniteAgentConfig(ctx context.Context, input gql_model.UpdateInfiniteAgentConfigInput) (*gql_model.UpdateInfiniteAgentConfigResponse, error) {
	infiniteAgentConfigResolver := resolvers.NewInfiniteAgentConfigResolver()
	return infiniteAgentConfigResolver.UpdateInfiniteAgentConfig(ctx, input)
}

// CreateReferralTreeSnapshot is the resolver for the createReferralTreeSnapshot field.
func (r *mutationResolver) CreateReferralTreeSnapshot(ctx context.Context) (*gql_model.CreateReferralTreeSnapshotResponse, error) {
	referralTreeResolver := resolvers.NewReferralTreeResolver()
	return referralTreeResolver.CreateReferralTreeSnapshot(ctx)
}

// CreateInfiniteAgentReferralTrees is the resolver for the createInfiniteAgentReferralTrees field.
func (r *mutationResolver) CreateInfiniteAgentReferralTrees(ctx context.Context) (*gql_model.CreateInfiniteAgentReferralTreeResponse, error) {
	infiniteAgentTreeResolver := resolvers.NewInfiniteAgentReferralTreeResolver()
	return infiniteAgentTreeResolver.CreateInfiniteAgentReferralTrees(ctx)
}

// ReferralSnapshot is the resolver for the referralSnapshot field.
func (r *queryResolver) ReferralSnapshot(ctx context.Context) (*gql_model.ReferralSnapshot, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.ReferralSnapshot(ctx)
}

// AgentLevels is the resolver for the agentLevels field.
func (r *queryResolver) AgentLevels(ctx context.Context) ([]*gql_model.AgentLevel, error) {
	levelResolver := resolvers.NewLevelResolver()
	return levelResolver.AgentLevels(ctx)
}

// AgentLevel is the resolver for the agentLevel field.
func (r *queryResolver) AgentLevel(ctx context.Context, id int) (*gql_model.AgentLevel, error) {
	levelResolver := resolvers.NewLevelResolver()
	return levelResolver.AgentLevel(ctx, id)
}

// UserLevelInfo is the resolver for the userLevelInfo field.
func (r *queryResolver) UserLevelInfo(ctx context.Context) (*gql_model.UserLevelInfoResponse, error) {
	levelResolver := resolvers.NewLevelResolver()
	return levelResolver.UserLevelInfo(ctx)
}

// DataOverview is the resolver for the dataOverview field.
func (r *queryResolver) DataOverview(ctx context.Context, input gql_model.DataOverviewInput) (*gql_model.DataOverviewWithSummary, error) {
	return r.DataOverviewService.DataOverview(ctx, input)
}

// TransactionData is the resolver for the transactionData field.
func (r *queryResolver) TransactionData(ctx context.Context, input gql_model.TransactionDataInput) (*gql_model.TransactionDataResponse, error) {
	transactionResolver := resolvers.NewTransactionResolver()
	return transactionResolver.TransactionData(ctx, input)
}

// InvitationRecords is the resolver for the invitationRecords field.
func (r *queryResolver) InvitationRecords(ctx context.Context, input gql_model.InvitationRecordRequest) (*gql_model.InvitationRecordResponse, error) {
	return r.RewardService.InvitationRecords(ctx, input)
}

// WithdrawalRecords is the resolver for the withdrawalRecords field.
func (r *queryResolver) WithdrawalRecords(ctx context.Context, input gql_model.WithdrawalRecordRequest) (*gql_model.WithdrawalRecordResponse, error) {
	return r.RewardService.WithdrawalRecords(ctx, input)
}

// InvitationSummary is the resolver for the invitationSummary field.
func (r *queryResolver) InvitationSummary(ctx context.Context) (*gql_model.InvitationSummaryResponse, error) {
	return r.InvitationService.InvitationSummary(ctx)
}

// InvitationList is the resolver for the invitationList field.
func (r *queryResolver) InvitationList(ctx context.Context, input gql_model.InvitationListRequest) (*gql_model.InvitationListResponse, error) {
	return r.InvitationService.InvitationList(ctx, input)
}

// GetClaimReward is the resolver for the getClaimReward field.
func (r *queryResolver) GetClaimReward(ctx context.Context) (*gql_model.ClaimRewardResponse, error) {
	claimResolver := resolvers.NewClaimResolver()
	return claimResolver.GetClaimReward(ctx)
}

// InfiniteAgentConfigs is the resolver for the infiniteAgentConfigs field.
func (r *queryResolver) InfiniteAgentConfigs(ctx context.Context) (*gql_model.InfiniteAgentConfigsResponse, error) {
	infiniteAgentConfigResolver := resolvers.NewInfiniteAgentConfigResolver()
	return infiniteAgentConfigResolver.InfiniteAgentConfigs(ctx)
}

// InfiniteAgentConfig is the resolver for the infiniteAgentConfig field.
func (r *queryResolver) InfiniteAgentConfig(ctx context.Context, id string) (*gql_model.InfiniteAgentConfigResponse, error) {
	infiniteAgentConfigResolver := resolvers.NewInfiniteAgentConfigResolver()
	return infiniteAgentConfigResolver.InfiniteAgentConfig(ctx, id)
}

// ReferralTreeSnapshots is the resolver for the referralTreeSnapshots field.
func (r *queryResolver) ReferralTreeSnapshots(ctx context.Context) (*gql_model.ReferralTreeSnapshotsResponse, error) {
	referralTreeResolver := resolvers.NewReferralTreeResolver()
	return referralTreeResolver.ReferralTreeSnapshots(ctx)
}

// ReferralTreeSnapshot is the resolver for the referralTreeSnapshot field.
func (r *queryResolver) ReferralTreeSnapshot(ctx context.Context, id string) (*gql_model.ReferralTreeSnapshotResponse, error) {
	referralTreeResolver := resolvers.NewReferralTreeResolver()
	return referralTreeResolver.ReferralTreeSnapshot(ctx, id)
}

// InfiniteAgentReferralTrees is the resolver for the infiniteAgentReferralTrees field.
func (r *queryResolver) InfiniteAgentReferralTrees(ctx context.Context) (*gql_model.InfiniteAgentReferralTreesResponse, error) {
	infiniteAgentReferralTreeResolver := resolvers.NewInfiniteAgentReferralTreeResolver()
	return infiniteAgentReferralTreeResolver.InfiniteAgentReferralTrees(ctx)
}

// InfiniteAgentReferralTree is the resolver for the infiniteAgentReferralTree field.
func (r *queryResolver) InfiniteAgentReferralTree(ctx context.Context, id string) (*gql_model.InfiniteAgentReferralTreeResponse, error) {
	infiniteAgentReferralTreeResolver := resolvers.NewInfiniteAgentReferralTreeResolver()
	return infiniteAgentReferralTreeResolver.InfiniteAgentReferralTree(ctx, id)
}

// TaskListByType is the resolver for the taskListByType field.
func (r *queryResolver) TaskListByType(ctx context.Context, input gql_model.TaskListByTypeInput) (*gql_model.TaskListByTypeResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TaskListByType(ctx, input)
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
